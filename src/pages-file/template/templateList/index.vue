<route lang="json5" type="page">
{
  needLogin: true,
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "模版库",
  },
}
</route>

<script lang="ts" setup>
import { getMyTemplateListAPI } from '@/api/ess/template/index'
import BusinessLineFilter from '@/components/filter-popup/business-line-filter.vue'
import ListSearch from '@/components/list-search/index.vue'
import { formatToDate } from '@/utils/dateUtil'

const paging = ref(null)
const loading = ref(true)
const dataList = ref([])

const filterShow = ref(false)

const queryParams = ref({
  templateName: null,
  flowFlag: 0,
})
async function getList(pageNo, pageSize) {
  try {
    const res = await getMyTemplateListAPI({
      limit: pageSize,
      page: pageNo,
      ...queryParams.value,
    })
    paging.value.complete(res.object.records)
  }
  finally {
    loading.value = false
  }
}

function handleQuery() {
  paging.value.reload()
}
</script>

<template>
  <view class="app-container">
    <z-paging
      ref="paging"
      v-model="dataList"
      hide-no-more-inside
      auto-show-system-loading
      @query="getList"
    >
      <template #top>
        <fg-navbar>模版库</fg-navbar>
        <view class="flex-x-between pr-20rpx">
          <ListSearch
            v-model="queryParams.templateName"
            placeholder="请输入模版名称"
            class="flex-auto"
            @change="handleQuery"
          />
          <BusinessLineFilter
            v-model:show="filterShow"
            v-model:checked-id="queryParams.businessLineId"
            trigger-type="icon"
            class="flex-shrink-0"
            @confirm="handleQuery"
          />
        </view>
      </template>
      <view class="temp-list">
        <view
          v-for="(item, index) in dataList"
          :key="index"
          class="temp-item" :class="[`temp-item-${(index % 4) + 1}`]"
        >
          <view class="title">
            {{ item.templateName }}
          </view>
          <view class="m-y-10rpx text-24rpx">
            {{ item.businessLineName }}
          </view>
          <view class="text-24rpx color-#6B6B6B">
            创建时间：{{ formatToDate(item.createTime) }}
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.temp-list {
  padding: 10rpx 30rpx;
  .temp-item {
    @apply bg-#fff p-y-30rpx p-x-33rpx rounded-18rpx mb-16rpx border-2 border-solid;
    .title {
      @apply title-line font-600 min-h-100rpx before:(top-8rpx translate-y-0);
    }

    // 第1个样式 - 绿色
    &.temp-item-1 {
      @apply bg-#f0f9f0 border-#4CAF50;
      .title {
        @apply before:bg-#4CAF50;
      }
    }

    // 第2个样式 - 橙色
    &.temp-item-2 {
      @apply bg-#fff5f0 border-#FF9800;
      .title {
        @apply before:bg-#FF9800;
      }
    }

    // 第3个样式 - 蓝色
    &.temp-item-3 {
      @apply bg-#f0f5ff border-#2196F3;
      .title {
        @apply before:bg-#2196F3;
      }
    }

    // 第4个样式 - 粉色
    &.temp-item-4 {
      @apply bg-#fdf0f5 border-#E91E63;
      .title {
        @apply before:bg-#E91E63;
      }
    }
  }
}
</style>
