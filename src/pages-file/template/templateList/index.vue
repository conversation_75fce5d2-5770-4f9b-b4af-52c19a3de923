<route lang="json5" type="page">
{
  needLogin: true,
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "模版库",
  },
}
</route>

<script lang="ts" setup>
import { getMyTemplateListAPI } from '@/api/ess/template/index'
import BusinessLineFilter from '@/components/filter-popup/business-line-filter.vue'
import ListSearch from '@/components/list-search/index.vue'

const paging = ref(null)
const loading = ref(true)
const dataList = ref([])

const filterShow = ref(false)

const queryParams = ref({
  templateName: null,
  flowFlag: 0,
})
async function getList(pageNo, pageSize) {
  try {
    const res = await getMyTemplateListAPI({
      limit: pageSize,
      page: pageNo,
      ...queryParams.value,
    })
    paging.value.complete(res.object.records)
  }
  finally {
    loading.value = false
  }
}

function handleQuery() {
  paging.value.reload()
}
</script>

<template>
  <view class="app-container">
    <z-paging
      ref="paging"
      v-model="dataList"
      hide-no-more-inside
      auto-show-system-loading
      @query="getList"
    >
      <template #top>
        <fg-navbar>模版库</fg-navbar>
        <view class="flex-x-between pr-20rpx">
          <ListSearch
            v-model="queryParams.templateName"
            placeholder="请输入模版名称"
            class="flex-auto"
            @change="handleQuery"
          />
          <BusinessLineFilter
            v-model:show="filterShow"
            v-model:checked-id="queryParams.businessLineId"
            trigger-type="icon"
            class="flex-shrink-0"
            @confirm="handleQuery"
          />
        </view>
      </template>
      <view class="temp-list">
        <view v-for="(item, index) in dataList" :key="index" class="temp-item">
          <view class="title">
            {{ item.templateName }}
          </view>
          <view>
            {{ item.businessLineName }}
          </view>
          <view> 创建时间：{{ item.createTime }} </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.temp-list {
  padding: 10rpx 30rpx;
  .temp-item {
    @apply bg-#fff p-y-30rpx p-x-33rpx rounded-18rpx mb-16rpx;
    .title {
      @apply title-line font-600 min-h-100rpx before:(top-10rpx translate-y-0);
    }
  }
}
</style>
