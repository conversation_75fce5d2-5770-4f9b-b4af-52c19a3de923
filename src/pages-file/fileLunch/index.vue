<route lang="json5">
{
  layout: "default",
  needLogin: true,
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "设置签署信息",
  },
}
</route>

<script lang="ts" setup>
import BusinessSelect from '.././components/BusinessSelect/index.vue'
import DictPicker from '.././components/DictPicker/index.vue'
import SelectSealUserModal from '.././components/SelectSealUserModal/index.vue'
import SelectSealUserTable from '.././components/SelectSealUserTable/index.vue'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const { proxy } = getCurrentInstance()
const { seal_type } = proxy.useDict('seal_type')
const selectSealUserModal = ref()

// 表单数据
const formData = ref({
  applyReason: '',
  businessLineId: '',
  unordered: false,
  approveDtoList: [],
  flowName: '',
  filePaths: [],
  flowType: '',
  deadline: '',
  selectSealUser: {},
})

// 添加签署方
function handleAddUser() {
  selectSealUserModal.value.open()
}

// 文件导入
function handleFileImport() {
  console.log('导入文件')
}

// 选择合同类型
function handleSelectContractType() {
  console.log('选择合同类型')
}

// 选择签署截止时间
function handleSelectDeadline() {
  console.log('选择签署截止时间')
}

// 表单验证规则
const rules = {

}
</script>

<template>
  <view class="file-lunch-page">
    <fg-navbar>设置签署信息</fg-navbar>

    <wd-form :model="formData" :rules="rules">
      <wd-cell title="发起原由" custom-class="input-reason" vertical required>
        <wd-textarea
          v-model="formData.applyReason"
          placeholder="请输入原由"
          :maxlength="500"
          show-word-limit
          custom-textarea-container-class="textarea-container"
        />
      </wd-cell>
      <wd-cell title="请选择文件所属业务线" vertical required>
        <BusinessSelect v-model="formData.businessLineId" />
      </wd-cell>
      <wd-cell title="签署方" vertical required>
        <div class="sign-method-selector">
          <div class="toggle-buttons">
            <div
              class="toggle-btn"
              :class="{ active: formData.unordered === false }"
              @click="formData.unordered = false"
            >
              无序签
            </div>
            <div
              class="toggle-btn"
              :class="{ active: formData.unordered }"
              @click="formData.unordered = true"
            >
              有序签
            </div>
          </div>
        </div>
        <div>
          <SelectSealUserTable />
        </div>
        <div style="height: 350rpx;display: flex;justify-content: center;align-items: center;background: #fff;border-radius: 10rpx;">
          <wd-button custom-class="btn-add-user" plain icon="add" @click="handleAddUser">
            添加签署方
          </wd-button>
        </div>
      </wd-cell>
      <wd-cell title="文件名称" vertical>
        <wd-input v-model="formData.flowName" />
      </wd-cell>
      <wd-cell title="文件内容" vertical>
        <wd-cell />
      </wd-cell>
      <wd-cell title="合同类型" vertical>
        <DictPicker v-model="formData.flowType" :options="seal_type" value-type="string" placeholder="请选择合同类型" />
      </wd-cell>
      <wd-cell title="签署截至" vertical>
        <wd-calendar v-model="formData.deadline" placeholder="请选择签署截止时间" />
      </wd-cell>
    </wd-form>

    <!-- 添加签署方弹窗 -->
    <SelectSealUserModal ref="selectSealUserModal" v-model="formData.approveDtoList" />
  </view>
</template>

<style lang="scss" scoped>
.file-lunch-page {
  min-height: 100vh;
}

.sign-method-selector {
  position: absolute;
  right: 0;
  top: -60rpx;

  .toggle-buttons {
    display: flex;
    border: 1rpx solid $uni-color-primary;
    border-radius: 50rpx;
    overflow: hidden;
    height: 48rpx;
    line-height: 40rpx;

    .toggle-btn {
      padding: 5rpx;
      font-size: 22rpx;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fff;
      color: $uni-color-primary;
      width: 115rpx;

      &.active {
        background: $uni-color-primary;
        color: #fff;
      }
    }
  }
}

:deep() {
  .wd-cell {
    border-radius: 8rpx;
    background-color: transparent !important;
    padding: 20rpx !important;

    .wd-cell__wrapper {
      padding: 0 !important;
    }
    .wd-cell__wrapper.is-vertical .wd-cell__right {
      margin-top: 15rpx !important;
    }

    .wd-cell__left.is-required::after {
      content: '';
      width: 12rpx;
      height: 12rpx;
      background-color: red;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
    }
  }
}
:deep(.input-reason) {
  .wd-textarea {
    border: #e0e0e0 solid 1rpx;
    padding: 16rpx 22rpx 10rpx 22rpx !important;
    border-radius: 10rpx;

    .wd-textarea__value {
      padding-bottom: 40rpx !important;
    }
    .wd-textarea__count {
      bottom: 0rpx !important;
    }
    .wd-textarea__inner {
      height: 260rpx;
    }
    .textarea-container {
      height: 300rpx;
    }
  }
}
:deep(.btn-add-user) {
  border-radius: 20rpx !important;
  border: 1rpx dashed $uni-color-primary !important;
  width: 365rpx;
  height: 85rpx !important;
  background: rgba(234, 248, 244, 0.4) !important;
}
</style>
