<script lang="ts" setup>
import { getHoldSealAPI, getSchoolLevelStaffAPI, getSecondLevelStaffAPI } from '@/api/ess/fileLunch/index'

const sealType = ref()
const sealTypeList = ref([])

async function getSealTypeList() {
  let res
  try {
    switch (sealType.value) {
      case 'schoolUserSelectRef':
        res = await getSchoolLevelStaffAPI()// 获取校级持章人
        break
      case 'secondUserSelectRef':
        res = await getSecondLevelStaffAPI()// 获取二级持章人
        break
      case 'holdSealUserRef':
        res = await getHoldSealAPI(0) // 获取持章人  0-校级 1-二级单位
        break
      default:
        res = await getSchoolLevelStaffAPI()
        break
    }
    sealTypeList.value = res.object
  }
  catch (error) {
    console.error(error)
  }
}
getSealTypeList()
</script>

<template>
  <view class="select-business">
    <fg-navbar>选择业务线</fg-navbar>

    <wd-cell-group>
      <wd-cell v-for="(item, index) in sealTypeList" :key="item.businessLineId" is-link :title="item.businessLineName" />
    </wd-cell-group>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
