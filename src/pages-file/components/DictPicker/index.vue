<script setup>
const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: [Number, String, Array],
    default: null,
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
  valueType: {
    type: String,
    default: 'number',
  },
})

const model = defineModel()
function formatterValue(e) {
  let result = props.valueType === 'number'
    ? e.map(item => ({
        ...item,
        value: Number(item.value),
      }))
    : e
  return result
}
</script>

<template>
  <div class="dict-select">
    <wd-picker
      v-model="model"
      :placeholder="placeholder"
      :columns="formatterValue(props.options)"
    />
  </div>
</template>

<style scoped lang="scss">
.dict-select {
  width: 100%;
}
</style>
