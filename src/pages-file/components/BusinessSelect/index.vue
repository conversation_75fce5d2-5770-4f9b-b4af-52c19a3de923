<script lang="ts" setup>
import { getBusinessLineListAPI } from '@/api/ess/baseSet/index'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
  ascriptionType: {
    // 归属 如果传入归属则数据进行筛选 // all | single
    default: 'all',
  },
  // 只展示文字
  showText: {
    type: Boolean,
    default: false,
  },
})
const { proxy } = getCurrentInstance()
const { business_ascription } = proxy.useDict('business_ascription')
const model = defineModel('modelValue')
const ascription = defineModel('ascription', { type: [String, Number], default: '0' })
const businessLineList = ref([])
const loading = ref(false)

// 获取业务线
async function getAll() {
  try {
    loading.value = true
    let res = await getBusinessLineListAPI()
    businessLineList.value = res.object
    loading.value = false
  }
  catch (error) {
    loading.value = false
  }
}

// 归属数据源
const ascriptionOptions = computed(() => {
  return business_ascription.value.filter(item =>
    props.ascriptionType !== 'all' ? +item.value === +ascription.value : true,
  )
})

// 获取对应业务线
function getBusinessList(type) {
  return businessLineList.value.filter(item => +item.ascription === +type)
}

// 改变业务线
function changeRadio(e) {

}
getAll()
</script>

<template>
  <view>
    <view v-for="item in ascriptionOptions" :key="item.dictValue" class="business-line-box__item">
      <view class="title">
        <text>{{ item.label }}</text>
        <wd-radio-group
          v-model="model"
          inline
          shape="dot"
          checked-color="#24a87f"
          @change="changeRadio"
        >
          <wd-radio v-for="i in getBusinessList(item.value)" :key="i.id" :value="i.id">
            {{ i.businessLineName }}
          </wd-radio>
        </wd-radio-group>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.business-line-box__item {
  &:not(:first-child) {
    margin-top: 20rpx;
  }
}
.title {
  background: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  text {
    font-weight: 600;
  }
}
:deep(.title) {
  .wd-radio.is-inline {
    margin-right: 50rpx !important;
  }
  .wd-radio__shape {
    border: 1rpx solid var(--wot-radio-dot-border-color, #dcdcdc) !important;
  }
  .wd-radio.is-dot .wd-radio__shape::before {
    background-color: $uni-color-primary !important;
  }

  .wd-radio.is-dot.is-checked .wd-radio__shape {
    background-color: #fff !important;
    border-color: $uni-color-primary !important;
  }
}
</style>
