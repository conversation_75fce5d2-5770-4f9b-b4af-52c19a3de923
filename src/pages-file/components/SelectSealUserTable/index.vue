<script setup lang="ts">
defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
// 是否为拖拽模式
const isDragMode = ref(false)

// 印章列表数据
const sealList = ref([
  {
    id: '1',
    name: '代表中山大学签署',
    color: '#24A87E',
    class: 'in-side',
    buttons: [
      { text: '添加用印人', action: 'addUser' },
      { text: '签字', action: 'sign' },
    ],
  },
  {
    id: '2',
    name: '代表校外企业签署',
    color: '#2680EA',
    class: 'out-side',
    buttons: [
      { text: '添加经办人', action: 'addAgent' },
    ],
  },
  {
    id: '3',
    name: '代表个人签署',
    color: '#FB9547',
    class: 'personal',
    buttons: [
      { text: '添加经办人', action: 'addAgent' },
    ],
  },
])

// 切换拖拽模式
function toggleDragMode() {
  isDragMode.value = !isDragMode.value
}

// 处理排序变化
function handleSortChange(newList) {
  console.log('排序变化:', newList)
  // 这里可以发送请求保存新的排序
}

// 处理按钮点击
function handleButtonClick(action, item) {
  console.log('按钮点击:', action, item)
}

// 处理删除
function handleDelete(index: number) {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个印章类型吗？',
    success: (res) => {
      if (res.confirm) {
        sealList.value.splice(index, 1)
      }
    },
  })
}
</script>

<template>
  <view class="select-seal-user-table">
    <view
      v-for="(item, index) in sealList"
      :key="item.id"
      class="seal-item"
      :class="item.class"
    >
      <view class="seal-type-tag" :style="{ backgroundColor: item.color }">
        {{ item.name }}
      </view>

      <view class="delete-icon" @click="handleDelete(index)">
        <wd-icon name="delete" size="30rpx" color="#999" />
      </view>

      <view class="action-buttons">
        <wd-button
          v-for="button in item.buttons"
          :key="button.text"
          plain
          round
          hairline
          :custom-class="item.class"
          @click="handleButtonClick(button.action, item)"
        >
          {{ button.text }}
        </wd-button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.select-seal-user-table {
  padding: 20rpx;
  background: #fff;
}

.seal-item {
  position: relative;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  min-height: 270rpx;
}

.seal-type-tag {
  display: inline-block;
  min-width: 200rpx;
  padding: 6rpx 24rpx;
  border-top-left-radius: 22rpx;
  border-bottom-right-radius: 22rpx;
  color: #fff;
  font-size: 24rpx;
  text-align: center;
  margin-bottom: 24rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding-top: 38rpx;
}

.delete-icon {
  position: absolute;
  top: 18rpx;
  right: 24rpx;
}

$in-side-bg: #f3faf8;
$out-side-bg: #f3f9fe;
$personal-bg: #fffaf5;
$in-side-color: $uni-color-primary;
$out-side-color: #2680ea;
$personal-color: #fb9547;
$button-font-size: 16rpx;

.in-side {
  background: $in-side-bg;
}

.out-side {
  background: $out-side-bg;
}

.personal {
  background: $personal-bg;
}

:deep(.action-buttons) {
  .wd-button {
    font-size: 25rpx !important;
    height: 65rpx !important;
    font-weight: lighter !important;
  }
}

:deep(.in-side) {
  background: $in-side-bg !important;
  border-color: $in-side-color !important;
  color: $in-side-color !important;
}

:deep(.out-side) {
  background: $out-side-bg !important;
  border-color: $out-side-color !important;
  color: $out-side-color !important;
}

:deep(.personal) {
  background: $personal-bg !important;
  border-color: $personal-color !important;
  color: $personal-color !important;
}
</style>
