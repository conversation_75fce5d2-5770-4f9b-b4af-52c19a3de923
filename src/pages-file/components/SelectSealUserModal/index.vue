<script setup>
const props = defineProps({
  modelValue: String,
})
const emit = defineEmits(['update:modelValue', 'select'])
const { proxy } = getCurrentInstance()
const visible = ref(false)
const current = ref(0)
const { seal_belong_subject } = proxy.useDict('seal_belong_subject')
// 印章类型
const tabs = ref([
  { label: '校级', value: 0, compName: 'schoolUserSelectRef', des: '校级印章多用于XXX场景' },
  { label: '二级单位', value: 1, compName: 'secondUserSelectRef' },
  { label: '印章持有人', value: 2, compName: 'holdSealUserRef' },
])

function open() {
  visible.value = true
}

function close() {
  visible.value = false
}
// 选择印章类型
function selectSealType(item) {
  current.value = item.value
  // uni.navigateTo({ url: `/pages-file/selectPages/selectBusinessLine/selectBusinessLine?sealType=${item.compName}` })
}
// 选择签署方
function select(item) {
  current.value = item.value
  emit('update:modelValue', item)
  close()
}

defineExpose({ open })
</script>

<template>
  <wd-overlay :show="true" :z-index="999" @click="visible = false">
    <view class="overlay-wrapper">
      <!-- 选择印章类型 -->
      <view v-if="false" class="choose-popup">
        <view class="type-title">
          请选择印章类型
        </view>
        <view class="close-icon">
          <wd-icon name="close" @click="close" />
        </view>
        <view
          v-for="item in tabs"
          :key="item.value"
          class="choose-popup-item" :class="[{ active: current === item.value }]"
          @click="selectSealType(item)"
        >
          <view class="choose-popup-content">
            <view class="choose-popup-title">
              {{ item.label }}
              <view class="type-sub-title">
                {{ item.des }}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 选择签署方 -->
      <view v-if="false" class="choose-popup">
        <view class="close-icon">
          <wd-icon name="close" @click="close" />
        </view>
        <view
          v-for="item in seal_belong_subject"
          :key="item.value"
          class="choose-popup-item" :class="[{ active: current === item.value }]"
          @click="select(item)"
        >
          <view class="choose-popup-content">
            <view class="choose-popup-title">
              代表{{ item.label }}签署
            </view>
          </view>
        </view>
      </view>
    </view>
  </wd-overlay>
</template>

<style scoped lang="scss">
.overlay-wrapper {
  @apply flex-center w-full h-full;
}
.choose-popup {
  border-radius: 18rpx;
  padding: 80rpx 50rpx 40rpx;
  width: 80%;
  box-sizing: border-box;
  background: linear-gradient(180deg, #daf0e9 0%, #ffffff 15%);
  position: relative;

  .type-title {
    text-align: center;
    margin-top: -24rpx;
    margin-bottom: 40rpx;
    font-weight: 600;
    font-size: 32rpx;
  }

  .close-icon {
    position: absolute;
    right: 0;
    top: 0;
    color: #d6d9da;
    padding: 20rpx 30rpx;
  }
}
.choose-popup-item {
  min-height: 55rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 48rpx;
  background: $uni-color-primary-light-8;
  border: 2rpx solid $uni-color-primary-light-7;
  border-radius: 18rpx;
  transition: background 0.2s;
  margin-bottom: 20rpx;
  &.active {
    background: $uni-color-primary;
    color: #fff;
    .choose-popup-desc {
      color: #fff;
    }
  }
  .choose-popup-title {
    text-align: center;
    font-size: 26rpx;
  }
}
</style>
