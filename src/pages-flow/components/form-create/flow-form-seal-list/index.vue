<script lang="ts" setup>
import { getSealListPageAPI } from '@/api/ess/seal/seal'
import ListSearch from '@/components/list-search/index.vue'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const props = defineProps<{
  sealId: string
}>()
const emit = defineEmits(['confirm'])
const loading = ref(true)
const paging = ref(null)

const checked = ref({})

const dataList = ref([])

const queryParams = ref({
  sealStatus: 'SUCCESS',
  sealName: null,
})

watch(
  () => dataList.value,
  (val) => {
    if (val) {
      if (props.sealId) {
        checked.value = dataList.value.find(item => item.id === props.sealId)
      }
    }
  },
)

const visible = ref(false)
function open() {
  visible.value = true
  console.log('open', visible.value)
}

async function getList(pageNo, pageSize) {
  try {
    const res = await getSealListPageAPI({
      limit: pageSize,
      page: pageNo,
      ...queryParams.value,
    })
    paging.value.complete(res.object.records)
  }
  finally {
    loading.value = false
  }
}

function handleSearch() {
  paging.value.reload()
}

// 返回
function handleClose() {
  checked.value = {}
  visible.value = false
}

function handleConfirm() {
  if (!checked.value?.id) {
    uni.showToast({
      title: '请选择印章',
      icon: 'none',
    })
    return
  }
  emit('confirm', checked.value)
  visible.value = false
}

defineExpose({
  open,
})
</script>

<template>
  <page-container
    :show="visible"
    position="right"
    :z-index="999"
    :overlay="true"
    class="seal-list-popup"
    @beforeleave="visible = false"
  >
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="1000"
      hide-no-more-inside
      auto-show-system-loading
      :paging-style="{ backgroundColor: '#f9f9f9' }"
      :z-index="999"
      @query="getList"
    >
      <template #top>
        <fg-navbar :left-click="handleClose">
          请选择印章
        </fg-navbar>
        <ListSearch v-model="queryParams.sealName" placeholder="请输入印章名称" @change="handleSearch" />
      </template>
      <view class="seal-list">
        <view
          v-for="(item, index) in dataList"
          :key="index"
          class="seal-card"
          @click="checked = item"
        >
          <view class="seal-card-header">
            <view class="flex-y-center">
              <view class="mr-8rpx flex-shrink-0">
                <wd-icon
                  v-if="item.id === checked?.id"
                  name="check-circle-filled"
                  size="34rpx"
                />
                <view v-else class="seal-card-icon" />
              </view>
              <view>{{ item.sealName }}</view>
            </view>
            <view class="seal-card-department">
              {{ item.deptName }}
            </view>
          </view>
          <view class="seal-card-body">
            <view class="seal-info-item">
              <view class="seal-info-label">
                印章编号
              </view>
              <view class="seal-info-value">
                {{ item.sealNo }}
              </view>
            </view>
            <view class="seal-info-item">
              <view class="seal-info-label">
                印章ID
              </view>
              <view class="seal-info-value">
                {{ item.id }}
              </view>
            </view>
            <view class="seal-info-item">
              <view class="seal-info-label">
                印章类型
              </view>
              <view class="seal-info-value">
                {{ item.sealType }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <template #bottom>
        <view class="bottom-btn">
          <wd-button type="info" @click="handleClose">
            取消
          </wd-button>
          <wd-button @click="handleConfirm">
            确定
          </wd-button>
        </view>
      </template>
    </z-paging>
  </page-container>
</template>

<style lang="scss" scoped>
.seal-list-popup {
  :deep(.z-paging-content-fixed) {
    z-index: 999;
  }
}
.seal-list {
  width: 100vw;
  background-color: #f9f9f9;
}

.seal-card {
  background-color: #fff;
  border-radius: 18rpx;
  width: 92%;
  margin: auto;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  overflow: hidden;
  .seal-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 20rpx 0 20rpx 30rpx;
    background: linear-gradient(to right, $uni-color-primary-light-8, #fff);
    font-weight: 600;
    :deep(.wd-icon) {
      color: $uni-color-primary;
    }
    .seal-card-icon {
      border: 2rpx solid #cecece;
      border-radius: 50%;
      width: 30rpx;
      height: 30rpx;
      background: #fff;
    }
  }
  .seal-card-department {
    background-color: $uni-color-primary-light-8;
    color: $uni-color-primary;
    padding: 8rpx 15rpx 8rpx 25rpx;
    border-radius: 100rpx 0 0 100rpx;
    max-width: 40%;
    min-width: 100rpx;
    @apply text-overflow text-24rpx;
  }
  .seal-card-body {
    padding: 15rpx 30rpx;
  }
  .seal-info-item {
    display: flex;
    font-size: 26rpx;
    margin-bottom: 16rpx;
    .seal-info-label {
      color: #86909c;
      margin-right: 14rpx;
      flex-shrink: 0;
      width: 130rpx;
    }
    .seal-info-value {
      color: #1d2129;
      word-break: break-all;
    }
  }
}

.bottom-btn {
  display: flex;
  gap: 30rpx;
  padding: 20rpx 20rpx max(20rpx, env(safe-area-inset-bottom));
  :deep() {
    .wd-button {
      flex: 1;
      height: 80rpx;
    }
  }
}
</style>
