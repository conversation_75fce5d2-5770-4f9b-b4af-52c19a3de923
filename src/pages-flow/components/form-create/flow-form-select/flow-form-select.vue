<!-- flow-form-select.vue -->
<script setup lang="ts">
import FlowFormSealList from '@/pages-flow/components/form-create/flow-form-seal-list/index.vue'
import { http } from '@/utils/http'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
const props = defineProps<{
  field: SelectField
  modelValue: string | number | Array<string | number>
  disabled?: boolean
  labelWidth?: string
  size?: string
  enableStyle?: boolean
  labelAlign?: string
  formData?: Record<string, any>
}>()

const emit = defineEmits(['update:modelValue', 'update:formData'])

interface SelectField {
  type: string
  field: string
  title: string
  options?: Array<{ label: string, value: string | number }>
  props?: Record<string, any>
  effect?: {
    fetch?: {
      action: string
      method?: string
      parse?: string
      query?: Record<string, any>
      data?: Record<string, any>
    }
  }
  $required?: boolean
}

const selectOptions = ref<Array<{ label: string, value: string | number }>>([])
const loading = ref(false)

// 判断是否需要从远程获取数据
const hasFetchEffect = computed(() => {
  return props.field.effect?.fetch?.action
})

// 默认数据映射
function mapDefaultData(data: any) {
  // 确保数据是数组格式
  if (!Array.isArray(data)) {
    if (data && typeof data === 'object') {
      // 如果是对象，尝试获取可能的数组属性
      const possibleArrays = ['list', 'data', 'items', 'records', 'rows']
      for (const key of possibleArrays) {
        if (Array.isArray(data[key])) {
          data = data[key]
          break
        }
      }
    }
    // 如果仍然不是数组，返回空数组
    if (!Array.isArray(data)) {
      return []
    }
  }

  return data.map((item: any) => ({
    label: item.label || item.dictLabel || item.name || item.title || String(item),
    value: item.value || item.dictValue || item.id || item.key || String(item),
  }))
}

// 智能解析函数 - 支持所有平台的统一解决方案
function parseDataWithPattern(parseStr: string, resData: any) {
  // 尝试从解析字符串中提取映射模式
  const labelMatch = parseStr.match(/label:\s*(\w+)\.(\w+)/)
  const valueMatch = parseStr.match(/value:\s*(\w+)\.(\w+)/)

  if (labelMatch && valueMatch) {
    const labelField = labelMatch[2] // 例如: deptName
    const valueField = valueMatch[2] // 例如: deptId

    if (Array.isArray(resData)) {
      return resData.map((item: any) => ({
        label: item[labelField] || item.label || item.name || String(item),
        value: item[valueField] || item.value || item.id || String(item),
      }))
    }
  }

  // 尝试其他常见的映射模式
  // 模式1: 简单的 res.map(item => ({...})) 格式
  const simpleMapMatch = parseStr.match(/res\.map\s*\(\s*\w+\s*=>\s*\(\s*\{\s*label:\s*\w+\.(\w+),?\s*value:\s*\w+\.(\w+)\s*\}\s*\)\s*\)/)
  if (simpleMapMatch) {
    const labelField = simpleMapMatch[1]
    const valueField = simpleMapMatch[2]

    if (Array.isArray(resData)) {
      return resData.map((item: any) => ({
        label: item[labelField] || item.label || item.name || String(item),
        value: item[valueField] || item.value || item.id || String(item),
      }))
    }
  }

  return mapDefaultData(resData)
}

// 执行动态解析函数
function executeParseFn(parseStr: string, resData: any) {
  try {
    const cleanedStr = parseStr
      .replace('[[FORM-CREATE-PREFIX-', '')
      .replace('-FORM-CREATE-SUFFIX]]', '')
      .replace(/\n/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()

    return parseDataWithPattern(cleanedStr, resData)
  }
  catch (error) {
    return mapDefaultData(resData)
  }
}

// 获取选项数据
async function fetchOptions() {
  if (!hasFetchEffect.value || props.field?.field === 'sealId')
    return

  const fetchConfig = props.field.effect!.fetch!
  loading.value = true

  try {
    const res = await http({
      url: fetchConfig.action,
      method: (fetchConfig.method as any) || 'GET',
      ...(fetchConfig.query && { params: fetchConfig.query }),
      ...(fetchConfig.data && { data: fetchConfig.data }),
    } as any)

    const resData = res?.object || (res as any)?.data

    if (fetchConfig.parse) {
      try {
        const parsedData = executeParseFn(fetchConfig.parse, resData)
        selectOptions.value = parsedData || []
      }
      catch (parseError) {
        selectOptions.value = mapDefaultData(resData) || []
      }
    }
    else {
      selectOptions.value = mapDefaultData(resData) || []
    }
  }
  catch (error) {
    selectOptions.value = []
  }
  finally {
    loading.value = false
  }
}

// 组件类型
const pickerType = computed(() => props.field.type === 'checkbox' ? 'checkbox' : 'radio')

// 监听 effect 变化
watch(
  () => props.field.effect,
  () => {
    if (hasFetchEffect.value) {
      fetchOptions()
    }
  },
  { immediate: true },
)

// 监听 options 变化
watch(
  () => props.field.options,
  (newOptions) => {
    if (!hasFetchEffect.value && newOptions) {
      selectOptions.value = newOptions
    }
  },
  { immediate: true },
)

const flowFormSealListRef = shallowRef()
function toSelectSeal() {
  if (props.disabled)
    return
  flowFormSealListRef.value.open()
  console.log('toSelectSeal', flowFormSealListRef.value)
}

function onConfirmSeal(seal: any) {
  emit('update:modelValue', seal.id)
  const obj = {
    ...props.formData,
    sealId: seal.id,
    sealIdShow: seal.id,
    sealNo: seal.sealNo,
    sealTypeName: seal.sealTypeName,
    sealDeptName: seal.deptName,
    sealDeptId: seal.deptId,
    sealName: seal.sealName,
  }
  emit('update:formData', obj)
}
</script>

<template>
  <view :class="{ 'select-seal': field?.field === 'sealId' }">
    <!-- 印章特定样式 -->
    <wd-cell
      v-if="field?.field === 'sealId'"
      vertical
      :prop="field.field"
    >
      <template #title>
        <view class="select-seal-title">
          {{ field.title }}
        </view>
      </template>
      <view class="select-seal-content" @click="toSelectSeal">
        <view class="name" :class="{ 'color-#333': formData.sealName }">
          {{ formData.sealName || (disabled ? '-' : '请选择印章') }}
        </view>
        <view v-if="!disabled" class="flex-shrink-0 text-primary">
          {{ formData.sealName ? '重新选择' : '去选择' }}<wd-icon name="arrow-right" size="22rpx" />
        </view>
      </view>
    </wd-cell>
    <wd-select-picker
      v-else
      :model-value="modelValue"
      :prop="field.field"
      :title="field.title"
      :label="field.title"
      align-right
      :size="size"
      :label-width="labelWidth"
      :show-confirm="false"
      :disabled="disabled"
      :required="!!field.$required"
      :placeholder="disabled ? '无' : field.props?.placeholder"
      :ellipsis="true"
      :type="pickerType"
      :max="field.props?.max || 0"
      :min="field.props?.min || 0"
      :columns="selectOptions"
      value-key="value"
      label-key="label"
      :loading="loading"
      :custom-label-class="
        enableStyle && labelAlign === 'right' ? 'form-label-class' : ''
      "
      :z-index="111"
      @change="(val: any) => emit('update:modelValue', val.value)"
    />

    <FlowFormSealList v-if="field?.field === 'sealId'" ref="flowFormSealListRef" :seal-id="formData.sealId" @confirm="onConfirmSeal" />
  </view>
</template>

<style lang="scss" scoped>
.select-seal {
  :deep(.wd-cell__left.is-required) {
    padding-left: 0;
    &::after {
      display: none;
    }
  }
  :deep(.wd-cell__right) {
    margin-top: 10rpx !important;
  }
  .select-seal-title {
    font-size: 26rpx;
    @apply dot before:(dot-12-#F94E4F);
  }
  .select-seal-content {
    font-size: 24rpx;
    background: #f7f8f9;
    border: 1rpx solid #e0e0e0;
    color: #aaaaaa;
    @apply flex-x-between p-x-20rpx rounded-10rpx h-70rpx;
  }
}
</style>
