<script lang="ts" setup>
import { myApproachingPage, otherApproachingPage } from '@/api/ess/flow/todo'
import EmptyListBox from '@/components/empty-list-box/index.vue'
import BusinessLineFilter from '@/components/filter-popup/business-line-filter.vue'
import ListSearch from '@/components/list-search/index.vue'
import { usePagingHeight } from '../usePagingHeight'
import TodoItemCard from './TodoItemCard.vue'
import WdRadioButton from './WdRadioButton.vue'

const { pagingHeight, calculateHeight } = usePagingHeight()

const { proxy } = getCurrentInstance()
const { flow_status } = proxy.useDict('flow_status')

const queryParams = ref({
  flowName: null,
  mine: 0,
  limit: 20,
  page: 1,
  businessLineId: null,
})
const loading = ref(false)
const paging = ref(null)
const dataList = ref([])

const filterShow = ref(false)

const typeOptions = ref([
  {
    label: '我发起的',
    value: 0,
    count: 0,
  },
  {
    label: '他人发起的',
    value: 1,
    count: 0,
  },
])

function onSearch() {
  handleQuery()
  getAllCount()
  nextTick(() => {
    queryParams.value.flowName ? calculateHeight('90rpx') : calculateHeight()
  })
}
// 改变筛选类型
function changeType(e) {
  queryParams.value.mine = e.value
  handleQuery()
}
function handleQuery() {
  paging.value.reload()
}
async function getList(pageNo, pageSize) {
  try {
    loading.value = true
    const reqData = {
      ...queryParams.value,
      limit: pageSize,
      page: pageNo,
    }
    const res
      = queryParams.value.mine === 0
        ? await getMineList(reqData)
        : await getOtherList(reqData)
    paging.value.complete(res.object.records)
  }
  finally {
    loading.value = false
  }
}

async function getMineList(reqData) {
  const res = await myApproachingPage(reqData)
  typeOptions.value[0].count = res.object.total
  return res
}
async function getOtherList(reqData) {
  const res = await otherApproachingPage(reqData)
  typeOptions.value[1].count = res.object.total
  return res
}

// 获取统计总数
function getAllCount() {
  const reqData = {
    ...queryParams.value,
    limit: 20,
    page: 1,
  }
  if (queryParams.value.mine === 0) { // 反着获取数量 避免重复请求
    getOtherList(reqData)
  }
  else {
    getMineList(reqData)
  }
}

getAllCount()
</script>

<template>
  <view class="">
    <view class="fixed-todo-header">
      <ListSearch v-model="queryParams.flowName" @change="onSearch" />
      <view v-if="queryParams.flowName" class="p-20rpx p-l-30rpx font-600">
        搜索结果
      </view>
      <view class="m-auto w-94% flex-x-between pb-5rpx pt-10rpx">
        <WdRadioButton
          :current="queryParams.mine"
          :options="typeOptions"
          @change="changeType"
        />
        <BusinessLineFilter
          v-model:show="filterShow"
          v-model:checked-id="queryParams.businessLineId"
          @confirm="handleQuery"
        />
      </view>
    </view>
    <z-paging
      ref="paging"
      v-model="dataList"
      auto-show-system-loading
      :fixed="false"
      :height="pagingHeight"
      @query="getList"
    >
      <view class="todo-list px-30rpx">
        <TodoItemCard
          v-for="item in dataList"
          :key="item.id"
          :item="item"
          type="file"
          :dict-options="{ flow_status }"
          show-file-status
        />
      </view>
      <template #loading>
        <fg-skeleton class="pt-20rpx" :loading="loading" />
      </template>
      <template #empty>
        <EmptyListBox />
      </template>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
