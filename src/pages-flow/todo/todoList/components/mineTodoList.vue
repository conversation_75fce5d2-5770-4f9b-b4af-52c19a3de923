<script lang="ts" setup>
import { fetchTodoPageAPI, mySigningPageAPI } from '@/api/ess/flow/todo'
import EmptyListBox from '@/components/empty-list-box/index.vue'
import BusinessLineFilter from '@/components/filter-popup/business-line-filter.vue'
import FlowGroupFilter from '@/components/filter-popup/flow-group-filter.vue'
import ListSearch from '@/components/list-search/index.vue'
import { usePagingHeight } from '../usePagingHeight'
import TodoItemCard from './TodoItemCard.vue'
import WdRadioButton from './WdRadioButton.vue'

const { pagingHeight, calculateHeight } = usePagingHeight()

const queryParams = ref({
  flowName: '',
  defFlowIds: [], // 添加defFlowIds字段
  limit: 20,
  page: 1,
})
const loading = ref(false)
const paging = ref(null)
const queryType = ref('flow') // flow待我审批 file待我签署
const dataList = ref([])
const isMounted = ref(false)

const filterShow = ref(false)

const typeOptions = ref([
  {
    label: '待我审批',
    value: 'flow',
    count: 0,
  },
  {
    label: '待我签署',
    value: 'file',
    count: 0,
  },
])

// 改变筛选类型
function changeType(e) {
  queryType.value = e.value
  handleQuery()
}
function handleQuery() {
  paging.value.reload()
}
// 搜索
function onSearch() {
  getAllCount()
  handleQuery()
  nextTick(() => {
    // 确保组件仍然挂载时才调用calculateHeight
    if (isMounted.value) {
      queryParams.value.flowName ? calculateHeight('90rpx') : calculateHeight()
    }
  })
}
async function getList(pageNo, pageSize) {
  try {
    loading.value = true
    const actionUrl = queryType.value === 'flow' ? fetchTodoPage : getSignTodoPage
    const res = await actionUrl(pageNo, pageSize)
    paging.value.complete(res.object.records)
  }
  finally {
    loading.value = false
  }
}

// 获取待我审批
async function fetchTodoPage(pageNo, pageSize) {
  const res = await fetchTodoPageAPI({
    flowName: queryParams.value.flowName,
    defFlowIds: queryParams.value.defFlowIds,
    current: pageNo,
    size: pageSize,
    belongType: '-1',
    descs: 'receive_time',
  })
  typeOptions.value[0].count = res.object.total
  return res
}

// 获取待我签署
async function getSignTodoPage(pageNo, pageSize) {
  const res = await mySigningPageAPI({
    flowName: queryParams.value.flowName,
    businessLineId: queryParams.value.businessLineId,
    page: pageNo,
    limit: pageSize,
  })
  typeOptions.value[1].count = res.object.total
  return res
}

watch(queryType, () => {
  queryParams.value.businessLineId = null
  queryParams.value.defFlowIds = null
  filterShow.value = false // 切换时关闭弹窗，需重新点击筛选
})

// 计算空状态标题
const emptyTitle = computed(() => {
  const hasFilter = queryParams.value.flowName || queryParams.value.defFlowIds
  return hasFilter ? '暂无搜索结果' : '清空也是一种快乐～'
})

// 获取统计总数
function getAllCount() {
  if (queryType.value === 'flow') {
    getSignTodoPage(1, 10)
  }
  else {
    fetchTodoPage(1, 10)
  }
}

getAllCount()

// 生命周期管理
onMounted(() => {
  isMounted.value = true
})

onUnmounted(() => {
  isMounted.value = false
})

onShow(async () => {
  await nextTick()
  paging.value?.refresh()
  getAllCount()
})
</script>

<template>
  <view class="">
    <view class="fixed-todo-header">
      <ListSearch v-model="queryParams.flowName" @change="onSearch" />
      <view v-if="queryParams.flowName" class="p-20rpx p-l-30rpx font-600">
        搜索结果
      </view>
      <view class="m-auto w-94% flex-x-between pb-5rpx pt-10rpx">
        <WdRadioButton :current="queryType" :options="typeOptions" @change="changeType" />
        <FlowGroupFilter
          v-if="queryType === 'flow'"
          v-model:show="filterShow"
          v-model:checked-id="queryParams.defFlowIds"
          @confirm="handleQuery"
        />
        <BusinessLineFilter
          v-else
          v-model:show="filterShow"
          v-model:checked-id="queryParams.businessLineId"
          @confirm="handleQuery"
        />
      </view>
    </view>
    <z-paging
      ref="paging"
      v-model="dataList"
      auto-show-system-loading
      :fixed="false"
      :height="pagingHeight"
      @query="getList"
    >
      <view class="todo-list px-30rpx">
        <TodoItemCard
          v-for="item in dataList"
          :key="item.id"
          :item="item"
          :type="queryType"
          is-hi-job="0"
          :search-keyword="queryParams.flowName"
        />
      </view>
      <template #loading>
        <fg-skeleton class="pt-20rpx" :loading="loading" />
      </template>
      <template #empty>
        <EmptyListBox :title="emptyTitle" />
      </template>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
