<script lang="ts" setup>
import {
  getDoingSignListAPI,
  onGoingFlowCountAPI,
  onGoingFlowPageAPI,
  queryFileDoingCountAPI,
} from '@/api/ess/flow/todo'
import EmptyListBox from '@/components/empty-list-box/index.vue'
import BusinessLineFilter from '@/components/filter-popup/business-line-filter.vue'
import FlowGroupFilter from '@/components/filter-popup/flow-group-filter.vue'
import ListSearch from '@/components/list-search/index.vue'
import { usePagingHeight } from '../usePagingHeight'
import TodoItemCard from './TodoItemCard.vue'
import WdRadioButton from './WdRadioButton.vue'

const { pagingHeight, calculateHeight } = usePagingHeight('fixed-todo-header', '410rpx')

const { proxy } = getCurrentInstance()

const { flow_status } = proxy.useDict('flow_status')

const queryParams = ref({
  flowName: null,
  mine: 0,
  defFlowIds: [], // 添加defFlowIds字段
})
const loading = ref(false)
const paging = ref(null)
const dataList = ref([])

const filterShow = ref(false)

const typeOptions = ref([
  {
    label: '我发起的',
    value: 0,
    count: 0,
  },
  {
    label: '他人发起的',
    value: 1,
    count: 0,
  },
])

const subTabOptions = ref([
  {
    label: '流程',
    value: 'flow',
    count: 0,
  },
  {
    label: '签署',
    value: 'file',
    count: 0,
  },
])

const isFlowOrFile = ref('flow')

function onSearch() {
  handleQuery()
  getTotalCount()
  nextTick(() => {
    queryParams.value.flowName ? calculateHeight('90rpx') : calculateHeight()
  })
}
// 改变筛选类型
function changeType(e) {
  queryParams.value.mine = e.value
  queryParams.value.businessLineId = null
  queryParams.value.defFlowIds = null
  getTotalCount()
  handleQuery()
}
function handleQuery() {
  getTotalCount()
  paging.value.reload()
}
async function getList(pageNo, pageSize) {
  try {
    loading.value = true
    const actionUrl = isFlowOrFile.value === 'flow' ? fetchTodoPage : getSignTodoPage
    const res = await actionUrl(pageNo, pageSize)
    paging.value.complete(res.object.records)
  }
  finally {
    loading.value = false
  }
}

// 流程
async function fetchTodoPage(pageNo, pageSize) {
  const res = await onGoingFlowPageAPI({
    mine: queryParams.value.mine,
    flowName: queryParams.value.flowName,
    defFlowIds: queryParams.value.defFlowIds,
    current: pageNo,
    size: pageSize,
  })
  return res
}

// 签署
async function getSignTodoPage(pageNo, pageSize) {
  const res = await getDoingSignListAPI({
    ...queryParams.value,
    page: pageNo,
    limit: pageSize,
  })
  return res
}

// 获取数量
async function getTotalCount() {
  const [signRes, flowRes] = await Promise.all([
    queryFileDoingCountAPI({ ...queryParams.value }),
    onGoingFlowCountAPI({ ...queryParams.value }),
  ])

  const { mine: signMine, other: signOther } = signRes.object
  const { mine: flowMine, other: flowOther } = flowRes.object

  // 总数赋值
  typeOptions.value[0].count = Number(signMine) + Number(flowMine)
  typeOptions.value[1].count = Number(signOther) + Number(flowOther)

  // 子tab赋值
  const isMine = queryParams.value.mine === 0
  subTabOptions.value[0].count = isMine ? flowMine : flowOther
  subTabOptions.value[1].count = isMine ? signMine : signOther
}

// 切换签署-流程
function changeFlowOrFile(e) {
  isFlowOrFile.value = e.value
  handleQuery()
}

getTotalCount()
</script>

<template>
  <view class="doing-list">
    <view class="fixed-todo-header">
      <ListSearch v-model="queryParams.flowName" @change="onSearch" />
      <view v-if="queryParams.flowName" class="p-20rpx p-l-30rpx font-600">
        搜索结果
      </view>
      <view class="m-auto w-94% flex-x-between pb-5rpx pt-10rpx">
        <WdRadioButton
          :current="queryParams.mine"
          :options="typeOptions"
          @change="changeType"
        />
        <FlowGroupFilter
          v-if="isFlowOrFile === 'flow'"
          v-model:show="filterShow"
          v-model:checked-id="queryParams.defFlowIds"
          @confirm="handleQuery"
        />
        <BusinessLineFilter
          v-else
          v-model:show="filterShow"
          v-model:checked-id="queryParams.businessLineId"
          @confirm="handleQuery"
        />
      </view>
      <view class="sub-tabs">
        <view
          v-for="item in subTabOptions"
          :key="item.value"
          class="sub-tabs__items"
          :class="{ active: item.value == isFlowOrFile }"
          @click="changeFlowOrFile(item)"
        >
          <view>
            <text>{{ item.label }}{{ item.count }}</text>
          </view>
        </view>
      </view>
    </view>
    <z-paging
      ref="paging"
      v-model="dataList"
      auto-show-system-loading
      :fixed="false"
      :height="pagingHeight"
      @query="getList"
    >
      <view class="todo-list px-30rpx">
        <TodoItemCard
          v-for="item in dataList"
          :key="item.id"
          :item="item"
          :type="isFlowOrFile"
          :dict-options="{ flow_status }"
          show-node
          :show-file-status="isFlowOrFile === 'file'"
        />
      </view>
      <template #loading>
        <fg-skeleton class="pt-20rpx" :loading="loading" />
      </template>
      <template #empty>
        <EmptyListBox />
      </template>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.sub-tabs {
  display: flex;
  align-items: center;
  height: 70rpx;
  &__items {
    font-size: 26rpx;
    padding: 0 30rpx;
    text {
      position: relative;
      z-index: 22;
    }
    view {
      padding: 0 5rpx;
    }
    &.active {
      view {
        @apply title-line-b;
        &:before {
          background: $uni-color-primary-light-6;
          border-radius: 10rpx;
        }
      }
    }
  }
}
</style>
