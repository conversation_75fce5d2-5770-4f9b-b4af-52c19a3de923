import { onReady } from '@dcloudio/uni-app'
import { getCurrentInstance, onMounted, ref } from 'vue'

/**
 * 计算z-paging高度（跨平台兼容）
 * @param selector 头部元素选择器（默认'.fixed-todo-header'）
 * @param h5Offset H5平台的额外偏移量（默认360rpx）
 * @returns 响应式的高度值
 */
export function usePagingHeight(selector = '.fixed-todo-header', h5Offset = '360rpx') {
  const pagingHeight = ref('70vh') // 默认值

  // 获取当前组件实例，在composable初始化时获取
  const instance = getCurrentInstance()

  const calculateHeight = (searchTitle = '1rpx') => {
    // #ifdef H5
    pagingHeight.value = `calc(100vh - ${h5Offset} - ${searchTitle})`
    // #endif

    // #ifdef MP-WEIXIN
    // 检查组件实例是否存在，避免在组件销毁后调用
    if (!instance) {
      console.warn('Component instance not available, using default height')
      pagingHeight.value = `calc(100vh - ${h5Offset} - 30rpx)`
      return
    }

    try {
      const query = uni.createSelectorQuery().in(instance)
      query.select(selector).boundingClientRect((res) => {
        // 再次检查组件实例是否仍然有效
        if (instance && res && !Array.isArray(res)) {
          pagingHeight.value = `calc(100vh - ${h5Offset} - 30rpx)`
        }
        else {
          // 如果查询失败，使用默认高度
          pagingHeight.value = `calc(100vh - ${h5Offset} - 30rpx)`
        }
      }).exec()
    }
    catch (error) {
      console.warn('Error calculating height:', error)
      pagingHeight.value = `calc(100vh - ${h5Offset} - 30rpx)`
    }
    // #endif
  }

  // 小程序用onReady确保DOM ready
  // #ifdef MP-WEIXIN
  onReady(calculateHeight)
  // #endif

  // H5用onMounted
  // #ifdef H5
  onMounted(calculateHeight)
  // #endif

  return { pagingHeight, calculateHeight }
}
