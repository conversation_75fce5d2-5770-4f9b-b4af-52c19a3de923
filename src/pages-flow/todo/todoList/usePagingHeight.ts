import { onReady } from '@dcloudio/uni-app'
import { getCurrentInstance, onMounted, ref } from 'vue'

/**
 * 计算z-paging高度（跨平台兼容）
 * @param selector 头部元素选择器（默认'.fixed-todo-header'）
 * @param h5Offset H5平台的额外偏移量（默认360rpx）
 * @returns 响应式的高度值
 */
export function usePagingHeight(selector = '.fixed-todo-header', h5Offset = '360rpx') {
  const pagingHeight = ref('70vh') // 默认值
  const instance = getCurrentInstance()

  const calculateHeight = (searchTitle = '1rpx') => {
    // #ifdef H5
    pagingHeight.value = `calc(100vh - ${h5Offset} - ${searchTitle})`
    // #endif

    // #ifdef MP-WEIXIN
    if (!instance) {
      pagingHeight.value = `calc(100vh - ${h5Offset} - 30rpx)`
      return
    }

    const query = uni.createSelectorQuery().in(instance)
    query.select(selector).boundingClientRect(() => {
      pagingHeight.value = `calc(100vh - ${h5Offset} - 30rpx)`
    }).exec()
    // #endif
  }

  // #ifdef MP-WEIXIN
  onReady(calculateHeight)
  // #endif

  // #ifdef H5
  onMounted(calculateHeight)
  // #endif

  return { pagingHeight, calculateHeight }
}
