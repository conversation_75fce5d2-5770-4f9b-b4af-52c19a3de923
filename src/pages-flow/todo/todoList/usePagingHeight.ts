import { onReady } from '@dcloudio/uni-app'
import { onMounted, ref } from 'vue'

/**
 * 计算z-paging高度（跨平台兼容）
 * @param selector 头部元素选择器（默认'.fixed-todo-header'）
 * @param h5Offset H5平台的额外偏移量（默认360rpx）
 * @returns 响应式的高度值
 */
export function usePagingHeight(selector = '.fixed-todo-header', h5Offset = '360rpx') {
  const pagingHeight = ref('70vh') // 默认值

  const calculateHeight = (searchTitle = '1rpx') => {
    // #ifdef H5
    pagingHeight.value = `calc(100vh - ${h5Offset} - ${searchTitle})`
    // #endif

    // #ifdef MP-WEIXIN
    const query = uni.createSelectorQuery().in(getCurrentInstance())
    query.select(selector).boundingClientRect((res) => {
      if (res && !Array.isArray(res)) {
        pagingHeight.value = `calc(100vh - ${h5Offset} - 30rpx)`
      }
    }).exec()
    // #endif
  }

  // 小程序用onReady确保DOM ready
  // #ifdef MP-WEIXIN
  onReady(calculateHeight)
  // #endif

  // H5用onMounted
  // #ifdef H5
  onMounted(calculateHeight)
  // #endif

  return { pagingHeight, calculateHeight }
}
