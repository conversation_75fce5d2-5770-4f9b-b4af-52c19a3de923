<script lang="ts" setup name="AuditPopup">
import { getConfigByConfigKeyAPI } from '@/api/system/index'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const emit = defineEmits(['submit'])
const show = ref(false)
const comment = ref(null) // 审批意见
const configKey = ref('agree') // 审批类型 agree:同意 reject:拒绝
const dataList = ref([])
const agreeList = ref([])
const rejectList = ref([])

const titleMap = computed(() => {
  const textMap = {
    agree: '审批通过',
    reject: '审批拒绝',
    backPre: '退回上一步',
  }
  return textMap[configKey.value]
})
function open(type = 'agree') {
  show.value = true
  configKey.value = type
  dataList.value = type === 'agree' ? agreeList.value : rejectList.value
}

function handleClose() {
  show.value = false
  comment.value = null
}

async function getConfigValue(key = 'flowCommentAgree') {
  try {
    const res = await getConfigByConfigKeyAPI(key)
    const list = res.object?.split(',').filter(Boolean) || []
    const target = key === 'flowCommentAgree' ? agreeList : rejectList
    target.value = list
  }
  catch (error) {}
}

function handleClick(item) {
  comment.value = item
}
function handleSubmit() {
  emit('submit', comment.value)
  handleClose()
}

getConfigValue('flowCommentAgree')
getConfigValue('flowCommentReject')

defineExpose({
  open,
})
</script>

<template>
  <view class="audit-popup">
    <wd-popup
      v-model="show"
      closable
      position="bottom"
      :z-index="99"
      @close="handleClose"
    >
      <view class="audit-form">
        <view class="audit-form-title">
          {{ titleMap }}意见
        </view>
        <view class="audit-form-textarea">
          <wd-textarea
            v-model="comment"
            :placeholder="`请填写${titleMap}意见`"
            :maxlength="500"
            show-word-limit
            fixed
          />
        </view>

        <view v-if="dataList.length" class="audit-form-help">
          <view class="audit-form-help-text">
            快捷填入
          </view>
          <view class="prompt-list">
            <view v-for="item in dataList" :key="item" class="prompt-list-item" @click="handleClick(item)">
              {{ item }}
            </view>
          </view>
        </view>
        <view class="handle-btn">
          <wd-button type="info" plain @click="handleClose">
            取消
          </wd-button>
          <wd-button @click="handleSubmit">
            确认
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
.audit-popup {
  :deep(.wd-popup) {
    border-radius: 20rpx 20rpx 0 0;
    .wd-popup__close {
      color: #a9a9af;
      font-size: 30rpx;
      padding: 5rpx;
    }
  }
}

.audit-form {
  min-height: 1000rpx;
  padding: 20rpx 30rpx;
  position: relative;
  overflow: hidden;
  .audit-form-title {
    font-size: 34rpx;
    font-weight: 500;
    margin-bottom: 20rpx;
  }
  .audit-form-textarea {
    background: #f9f9f9;
    border-radius: 16rpx;
    border: 1rpx solid #e0e0e0;
    padding: 0;
    min-height: 340rpx;
    --wot-textarea-bg: transparent;
    :deep(.wd-textarea__value) {
      background: transparent;
      padding-bottom: 30rpx;
      .wd-textarea__inner {
        height: 260rpx;
        padding: 0;
      }
      .wd-textarea__count {
        background: transparent;
        bottom: -14rpx;
        right: 0;
      }
    }
  }

  .handle-btn {
    display: flex;
    gap: 20rpx;
    position: absolute;
    width: 92%;
    left: 0;
    right: 0;
    margin: 0 auto;
    padding-bottom: 40rpx;
    bottom: 0;
    :deep(.wd-button) {
      flex: 1;
      height: 90rpx;
    }
  }
  .audit-form-help-text {
    font-size: 26rpx;
    margin: 40rpx 0 20rpx;
  }
  .prompt-list {
    display: flex;
    flex-wrap: wrap;
    .prompt-list-item {
      background: #f8f8f8;
      padding: 14rpx 20rpx;
      border-radius: 100rpx;
      margin-right: 20rpx;
      min-width: calc(25% - 20rpx);
      box-sizing: border-box;
      text-align: center;
      margin-bottom: 20rpx;
      color: #6b6b6b;
      font-size: 26rpx;
    }
  }
}
</style>
