import type { ICaptcha, IUpdateInfo, IUpdatePassword, IUserInfoVo, IUserLogin } from './types/login'
import { auth, eduUser } from '@/config/constant'
import { http } from '@/utils/http'

/**
 * 登录表单
 */
export interface ILoginForm {
  username: string
  password: string
  code: string
  uuid: string
}

/**
 * 获取验证码
 * @returns ICaptcha 验证码
 */
export function getCode() {
  return http.get<ICaptcha>(`/code`)
}

/**
 * 用户登录
 * @param loginForm 登录表单
 */
export function login(loginForm: ILoginForm) {
  return http.post<IUserLogin>(`${auth}/auth/login`, loginForm)
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return http.get<IUserInfoVo>(`${eduUser}/system/user/getInfo`)
}

/**
 * 查询用户角色部门列表信息
 */
export function getRoleDeptInfos(userName: IUserInfoVo) {
  return http.get<IUserInfoVo>(`${eduUser}/zsRoleInfoApi/selectZsRoleDeptInfos/${userName}`)
}

/**
 * 切换角色部门
 */
export function switchRoleLogin(data: IUserInfoVo) {
  return http.post<IUserInfoVo>(`${auth}/auth/zsSwitchRole`, data)
}

/**
 * ess 登录 zsLogin
 */
export function zsLogin(data: IUserInfoVo) {
  return http.post<IUserInfoVo>(`${auth}/auth/zsLogin`, data, null, { istToken: false })
}

/**
 * 退出登录
 */
export function logout() {
  return http.get<void>(`${eduUser}/system/user/logout`)
}

/**
 * 修改用户信息
 */
export function updateInfo(data: IUpdateInfo) {
  return http.post(`${eduUser}/system/user/updateInfo`, data)
}

/**
 * 修改用户密码
 */
export function updateUserPassword(data: IUpdatePassword) {
  return http.post(`${eduUser}/system/user/updatePassword`, data)
}

/**
 * 获取微信登录凭证
 * @returns Promise 包含微信登录凭证(code)
 */
export function getWxCode() {
  return new Promise<UniApp.LoginRes>((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: res => resolve(res),
      fail: err => reject(new Error(err)),
    })
  })
}

/**
 * 获取企业微信登录凭证
 * @returns Promise 包含微信登录凭证(code)
 */
export function getQyWxCode() {
  return new Promise<UniApp.LoginRes>((resolve, reject) => {
    wx.qy.login({
      success: res => resolve(res),
      fail: err => reject(new Error(err)),
    })
  })
}

/**
 * 微信登录
 * @param params 微信登录参数，包含code
 * @returns Promise 包含登录结果
 */
export function wxLogin(data: { code: string }) {
  return http.post<IUserLogin>(`${auth}/auth/loginQyWeCom`, data, null, { istToken: false })
}

/**
 * 企业微信登录
 * @param params 微信登录参数，包含code
 * @returns Promise 包含登录结果
 */
export function qyWxLogin(data: { code: string }) {
  return http.post<IUserLogin>(`${auth}/mobileAuth/loginQyWeCom`, data, null, { istToken: false })
}
