import { essPrefix } from '@/config/constant'
import { http } from '@/utils/http'

// 查询
export function getBusinessLineListAPI() {
  return http.get(`${essPrefix}/businessLine/list`)
}

// 获取校级持章人
export function getSchoolLevelStaffAPI() {
  return http.get(`${essPrefix}/signatory/schoolLevelStaff`)
}
// 获取二级持章人
export function getSecondLevelStaffAPI() {
  return http.get(`${essPrefix}/signatory/secondLevelStaff`)
}
// 持章人  0-校级 1-二级单位
export function getHoldSealAPI(staffType: number) {
  return http.get(`${essPrefix}/signatory/holdSealStaff/${staffType}`)
}
