<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
  {
  layout: "tabbar",
  style: {
  navigationStyle: "custom",
  navigationBarTitleText: "个人中心",
  },
  }
  </route>

<script lang="ts" setup>
import ConfirmModal from '@/components/confirm-modal/index.vue'
import { useUserStore } from '@/store'
import MainUserInfo from './components/MainUserInfo.vue'
import UseSealChart from './components/UseSealChart.vue'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
const userInfo = ref({ loginRoles: [] })
const loading = ref(true)
const sealData = ref({
  signed: 121,
  pending: 42,
})
const confirmModal = ref()

function getUserProfile() {
  useUserStore()
    .getUserInfo()
    .then((res) => {
      userInfo.value = {
        ...res.user,
        loginDeptName: res.loginDeptName,
        loginRoles: res.loginRoles,
        deptRoleInfos: res.deptRoleInfos,
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 登出
function handleLogout() {
  confirmModal.value.open('是否确定登出？')
}

async function logOut() {
  await useUserStore().logout()
}
onLoad(() => {
  getUserProfile()
})
</script>

<template>
  <view class="mine-container">
    <MainUserInfo :user-info="userInfo" />
    <UseSealChart class="useChartView" :data="sealData" :user-info="userInfo" />
    <view class="btnView">
      <wd-button custom-class="btn-logout" type="info" plain hairline @click="handleLogout">
        退出登录
      </wd-button>
    </view>
    <!-- 退出登录确认框 -->
    <ConfirmModal ref="confirmModal" @confirm="logOut" />
  </view>
</template>

<style lang="scss" scoped>
.mine-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 94px);
}

.useChartView {
  margin-top: -260rpx;
  z-index: 2;
}

.btnView {
  display: flex;
  justify-content: center;
  width: 100%;
  margin: 85rpx 0;

  :deep() {
    .btn-logout {
      width: 420rpx;
      height: 80rpx !important;
      background: transparent !important;
      color: #707070 !important;
    }
  }
}
</style>
