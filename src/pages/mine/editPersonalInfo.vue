<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
style: {
navigationStyle: "custom",
navigationBarTitleText: "个人信息",
},
}
</route>

<script setup lang="ts">
import { eduUser, imageApi } from '@/config/constant'
import { useUserStore } from '@/store/user'

import EditNickName from './components/EditNickName.vue'
import SwitchRolePopup from './components/SwitchRolePopup.vue'

const userInfo = ref({})
const switchRolePopup = ref()
const editNickName = ref()
const loading = ref(false)
// 切换部门弹窗
function toSwitchRole() {
  switchRolePopup.value.open()
}

// 是否能切换部门标记
const isSingleDept = computed(() => {
  return userInfo.value && userInfo.value.deptRoleInfos.length > 1 || false
})

// 切换头像
async function handleAvatar() {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    success: async (res) => {
      uni.uploadFile({
        url: `${eduUser}/system/user/profile/avatar`,
        filePath: res.tempFiles[0].path,
        name: 'avatarfile',
        header: { 'Content-Type': 'application/x-www-form-urlencoded' },
        success: async (res) => {
          await getUserProfile()
        },
      })
    },
  })
}

// 修改昵称
async function handleNickName() {
  editNickName.value.open(userInfo.value.nickName)
}

async function getUserProfile() {
  loading.value = true
  const res = await useUserStore().getUserInfo()
  userInfo.value = {
    ...res.user,
    deptRoleInfos: res.deptRoleInfos,
    loginRoles: res.loginRoles.map(role => role.roleName).join('/'),
  }
  loading.value = false
}

onLoad(async () => {
  await getUserProfile()
})
</script>

<template>
  <view class="personal-info-container">
    <fg-navbar>个人信息</fg-navbar>
    <wd-skeleton animation="gradient" :custom-style="{ width: '100%' }" :row-col="[1, 1, 1, 1]" :loading="loading" />
    <view v-if=" !loading" class="info-list">
      <wd-cell-group>
        <wd-cell clickable center @click="handleAvatar">
          <template #title>
            <view class="cell-custom-title">
              <image src="@/static/images/icon/info-avatar.png" />
              头像
            </view>
          </template>
          <template #default>
            <view class="avatar-container">
              <image class="avatar" :src="imageApi + userInfo.avatar" mode="aspectFill" />
            </view>
          </template>
        </wd-cell>
        <wd-cell :value="userInfo.nickName" clickable @click="handleNickName">
          <template #title>
            <view class="cell-custom-title">
              <image src="@/static/images/icon/info-name.png" />
              姓名
            </view>
          </template>
          <template #right-icon>
            <wd-icon name="edit" size="16px" color="#8D97A8" />
          </template>
        </wd-cell>
        <wd-cell :value="userInfo.loginRoles" :clickable="isSingleDept" @click="toSwitchRole">
          <template #title>
            <view class="cell-custom-title">
              <image src="@/static/images/icon/info-role.png" />
              账号角色
            </view>
          </template>
          <template v-if="isSingleDept" #right-icon>
            <wd-icon name="edit" size="16px" color="#8D97A8" />
          </template>
        </wd-cell>
      </wd-cell-group>

      <wd-cell-group>
        <wd-cell center>
          <template #title>
            <view class="cell-custom-title">
              <image src="@/static/images/icon/info-real-name.png" />
              <span>是否实名</span>
            </view>
          </template>
          <template #default>
            <view class="verified-status">
              <div v-if="+userInfo.izRealAuth === 1 ">
                <image src="@/static/images/icon/real-name.png" mode="widthFix" />
                已实名
              </div>
              <div v-if="+userInfo.izRealAuth !== 1 ">
                <image src="@/static/images/icon/un-real.png" mode="widthFix" />
                未实名，
                <text class="color-#24A87E underline">
                  实名链接推送
                </text>
              </div>
            </view>
          </template>
        </wd-cell>
        <wd-cell :value="userInfo.userName">
          <template #title>
            <view class="cell-custom-title">
              <image src="@/static/images/icon/info-netId.png" />
              NetId
            </view>
          </template>
        </wd-cell>
        <wd-cell :value="userInfo.workNumber">
          <template #title>
            <view class="cell-custom-title">
              <image src="@/static/images/icon/info-workId.png" />
              工号
            </view>
          </template>
        </wd-cell>
        <wd-cell :value="userInfo.fileDeptName">
          <template #title>
            <view class="cell-custom-title">
              <image src="@/static/images/icon/info-dept.png" />
              人事部门
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>
    <!-- 角色切换弹窗 -->
    <SwitchRolePopup ref="switchRolePopup" />
    <!-- 修改昵称头像 -->
    <EditNickName ref="editNickName" @success="getUserProfile" />
  </view>
</template>

<style lang="scss" scoped>
.personal-info-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;

  .title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }

  .placeholder {
    width: 20px;
  }
}

.info-list {
  padding: 0 20rpx;

  .cell-custom-title {
    display: flex;
    align-items: center;

    image {
      width: 35rpx;
      height: 35rpx;
      padding-right: 18rpx;
    }
  }
}

.avatar-container {
  .avatar {
    width: 75rpx !important;
    height: 75rpx !important;
    border-radius: 50%;
    background-color: #f0f0f0;
  }
}

.verified-status {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  image {
    width: 22rpx;
  }
}

:deep(.wd-cell-group) {
  border-radius: 18rpx;
  padding: 15rpx 0;
}

:deep(.wd-cell-group:nth-child(2)) {
  margin-top: 20rpx;
}

:deep(.wd-cell-group__body) {
  border-radius: 18rpx;
}

:deep(.wd-cell) {
  padding: 10rpx 0;
  padding-left: 0 !important;
  border-radius: 18rpx;

  .wd-cell__wrapper {
    padding: 10rpx 20rpx 10rpx 25rpx;
  }
  .wd-cell__right {
    width: 372rpx;
  }

  .wd-cell__title .wd-cell__value {
    font-size: 16px !important;
    color: #0c1433 !important;
  }

  .wd-cell__value {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding-right: 10rpx;
  }
}

:deep(.wd-skeleton__col) {
  height: 100rpx !important;
  margin: 0 20rpx;
}
</style>
