<script lang="ts" setup>
import { getHomeModuleList } from '@/api/home/<USER>'
import IconNav from './IconNav.vue'

const items = ref([
  {
    title: '常用流程',
    moduleType: 0,
    items: [],
  },
  {
    title: '工具箱',
    moduleType: 1,
    items: [],
  },
  {
    title: '实体印章',
    moduleType: 2,
    items: [],
  },
])
const loading = ref(true)
function getList(moduleType) {
  getHomeModuleList({ moduleType, appType: 1 })
    .then((res) => {
      items.value[moduleType].items = res.object
    })
    .finally(() => {
      loading.value = false
    })
}

function toMore(item) {
  uni.navigateTo({
    url: `/pages/index/moduleMorePage?moduleType=${item.moduleType}`,
  })
}

getList(0) // 常用流程
getList(1) // 工具箱
getList(2) // 实体印章
</script>

<template>
  <view class="module-nav">
    <template v-for="item in items" :key="item.moduleType">
      <view v-if="item.items.length" class="module-nav-item">
        <view class="module-nav-item-title">
          <view class="title">
            {{ item.title }}
          </view>
          <view v-if="item.items.length > 4" class="flex-y-center text-26rpx color-#9B9B9B" @click="toMore(item)">
            <text> 更多 </text>
            <wd-icon name="arrow-right" size="24rpx" />
          </view>
        </view>
        <view class="module-nav-item-list">
          <IconNav
            v-for="child in item.items.slice(0, 4)"
            :key="child.id"
            :info="child"
            class="item-card"
          />
        </view>
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
.module-nav {
  margin-top: 20rpx;
  .module-nav-item {
    margin-bottom: 36rpx;
    .module-nav-item-title {
      @apply mb-20rpx flex-x-between;
      .title {
        @apply title-line;
      }
    }
    .module-nav-item-list {
      display: flex;
      flex-wrap: nowrap;
      .item-card {
        width: 24%;
        margin-right: 1%;
        flex-shrink: 0;
        font-size: 26rpx;
      }
    }
  }
}
</style>
