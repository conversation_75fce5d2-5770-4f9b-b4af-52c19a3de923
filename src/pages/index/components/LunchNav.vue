<script lang="ts" setup>
import ConfirmModal from '@/components/confirm-modal/index.vue'
import ChoosePopup from './ChoosePopup.vue'

const confirmModalRef = ref()
const choosePopupRef = ref()
const isFileLunch = ref(false) // 是否是文件发起
const showChooseFile = ref(false) // 选择文件
function openQuickLunch() {
  isFileLunch.value = true
  confirmModalRef.value.open(
    '小程序暂不提供发起事项审批功能，即发起后直接进入用印，如需走事项审批，请前往PC端进行发起。',
  )
}
function openTemplateLunch() {
  isFileLunch.value = false
  confirmModalRef.value.open(
    '小程序模板发起只提供无需审批的模板进行发起，如需走事项审批，请前往PC端进行发起',
  )
}

function handleConfirm() {
  if (isFileLunch.value) {
    showChooseFile.value = true
  }
  else {
    uni.navigateTo({
      url: '/pages-file/template/templateList/index',
    })
  }
}
</script>

<template>
  <view class="lunch-nav">
    <div class="lunch-nav-item" @click="openQuickLunch">
      <image src="@/static/images/index/tab-left.png" mode="scaleToFill" />
      <div class="content">
        <div class="title">
          快速发起合同
        </div>
        <div class="desc">
          文件/图片、草稿
        </div>
      </div>
    </div>
    <div class="lunch-right lunch-nav-item" @click="openTemplateLunch">
      <image src="@/static/images/index/tab-right.png" mode="scaleToFill" />
      <div class="content">
        <div class="title">
          模板发起合同
        </div>
        <div class="desc">
          拥有的模板
        </div>
      </div>
    </div>

    <ConfirmModal ref="confirmModalRef" @confirm="handleConfirm" />
    <ChoosePopup ref="choosePopupRef" v-model="showChooseFile" />
  </view>
</template>

<style lang="scss" scoped>
.lunch-nav {
  height: 180rpx;
  position: relative;
  margin-top: -60rpx;
}

.lunch-nav-item {
  position: absolute;
  width: 50%;
  height: 100%;
  left: 0;

  image {
    width: 100%;
    height: 100%;
  }
  .content {
    color: #fff;
    position: absolute;
    left: 30rpx;
    top: 45rpx;
    @apply text-24rpx;
    .title {
      @apply text-30rpx font-600 mb-10rpx;
    }
  }
  &.lunch-right {
    right: 0;
    left: auto;
    .content {
      left: 50rpx;
    }
  }
}
</style>
