<route lang="json5">
{
style: {
navigationStyle: "custom",
navigationBarTitleText: "登录",
},
}
</route>

<script setup lang="ts">
import { getCode } from '@/api/login'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const title = computed(() => {
  return import.meta.env.VITE_APP_TITLE
})
// 储存索引KEY
const userNameKey = 'userName'
const passwordKey = 'userPassword'
const rememberKey = 'rememberPassword'

// 表单引用
const formRef = ref()

// 登录表单数据
const loginForm = ref({
  username: uni.getStorageSync(userNameKey) || '',
  password: uni.getStorageSync(passwordKey) || '',
  code: '',
  uuid: '',
  rememberPassword: Boolean(uni.getStorageSync(rememberKey)) || false,
})

// 登录加载状态
const loginLoading = ref(false)
const codeUrl = ref('')
// 验证码开关
const captchaEnabled = ref(true)

const rules = {
  username: [
    {
      required: true,
      message: '请输入用户名',
    },
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
    },
  ],
  code: [
    {
      required: true,
      message: '请输入验证码',
    },
  ],
}

// 刷新验证码
async function refreshCaptcha() {
  try {
    const res = await getCode()
    captchaEnabled.value = res.captchaOnOff ?? true
    if (captchaEnabled.value) {
      codeUrl.value = `data:image/gif;base64,${res.img}`
      loginForm.value.uuid = res.uuid
    }
  }
  catch (error) {
    console.error('获取验证码失败:', error)
  }
}

// 记住密码处理
async function handleRemember() {
  const { rememberPassword, username, password } = loginForm.value

  if (rememberPassword) {
    uni.setStorageSync(userNameKey, username)
    uni.setStorageSync(passwordKey, password)
    uni.setStorageSync(rememberKey, true)
  }
  else {
    uni.removeStorageSync(userNameKey)
    uni.removeStorageSync(passwordKey)
    uni.removeStorageSync(rememberKey)
  }
}

// 登录
async function handleLogin() {
  try {
    const valid = await formRef.value.validate()
    if (!valid.valid) {
      return
    }
    loginLoading.value = true
    await handleRemember()
    await useUserStore().login(loginForm.value)
  }
  catch (error) {
    refreshCaptcha()
  }
  finally {
    loginLoading.value = false
  }
}

// 企微登录
async function handleWechatLogin() {
  try {
    loginLoading.value = true
    await useUserStore().qyWxLogin()
  }
  catch (error) {
    refreshCaptcha()
  }
  finally {
    loginLoading.value = false
  }
}
handleWechatLogin()
refreshCaptcha()
</script>

<template>
  <view class="login-container">
    <image class="login-bg" src="@/static/images/login/login-bg.jpg" />

    <view class="logo-section">
      <image src="@/static/logo.png" mode="aspectFit" />
      <span>{{ title }}</span>
    </view>

    <view class="login-form">
      <wd-form ref="formRef" :model="loginForm" :rules="rules">
        <wd-input v-model="loginForm.username" prop="username" clearable placeholder="请输入账号">
          <template #prefix>
            <image src="@/static/images/login/login-user.png" mode="widthFix" />
          </template>
        </wd-input>
        <wd-input v-model="loginForm.password" prop="password" clearable show-password placeholder="请输入密码">
          <template #prefix>
            <image src="@/static/images/login/login-password.png" mode="widthFix" />
          </template>
        </wd-input>
        <view v-if="captchaEnabled" class="captcha">
          <wd-input v-model="loginForm.code" prop="code" clearable placeholder="请输入验证码">
            <template #prefix>
              <image src="@/static/images/login/login-captcha.png" mode="widthFix" />
            </template>
          </wd-input>
          <view class="img-captcha-box">
            <image :src="codeUrl" @click="refreshCaptcha" />
          </view>
        </view>
        <view class="remember">
          <wd-checkbox v-model="loginForm.rememberPassword" color="#52c41a">
            记住密码
          </wd-checkbox>
        </view>
        <view class="footer">
          <wd-button type="primary" custom-class="login-btn" :disabled="loginLoading" :loading="loginLoading" @click="handleLogin">
            登录
          </wd-button>
        </view>
      </wd-form>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import url('@/assets/fonts/font.scss');

.login-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  z-index: 1;

  .login-bg {
    width: 100%;
    height: 100%;
    z-index: -1;
    position: absolute;
    background-size: cover;
    background-repeat: no-repeat;
  }
}

.logo-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 260rpx;
  font-family: 'DingTalk';
  font-weight: 600;
  font-size: 45rpx;

  image {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 20rpx;
  }
}

.login-form {
  margin: 150rpx 80rpx 0 80rpx;
}

.captcha {
  display: flex;
  align-items: center;

  .img-captcha-box {
    margin-left: 40rpx;
    border-radius: 10rpx;
    width: 280rpx;
    height: 90rpx;
    overflow: hidden;

    image {
      height: 100%;
      width: 100%;
    }
  }
  :deep(.wd-input) {
    margin-bottom: 0;
    width: 420rpx;
  }
}

.remember {
  margin: 60rpx 0 0 30rpx;
}

.footer {
  margin-top: 50rpx;
}

:deep() {
  .wd-input {
    margin-bottom: 45rpx;
    padding: 0 40rpx;
    height: 90rpx;
    border-radius: 60rpx;
    border: $uni-color-primary-light-7 2rpx solid;

    image {
      width: 30rpx;
    }

    .wd-input__prefix {
      display: flex;
      align-items: center;
      height: 90rpx;
    }

    .wd-input__suffix {
      display: flex;
      align-items: center;
      height: 90rpx;
    }

    .wd-input__value {
      height: 100% !important;
    }

    .wd-input__body {
      height: 100% !important;
    }
  }

  .wd-input.is-not-empty::after,
  .wd-input:not(.is-disabled)::after {
    display: none !important;
    content: none !important;
  }

  .login-btn {
    width: 100%;
    height: 95rpx !important;
    font-size: 32rpx !important;
  }
}
</style>
